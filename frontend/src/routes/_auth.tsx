import { createFileRoute, Outlet, redirect } from '@tanstack/react-router'

export const Route = createFileRoute('/_auth')({
  beforeLoad: async ({ location, context }) => {
    console.log(context)
    if (!context.auth.isAuthenticated) {
      throw redirect({
        to: "/login",
        search: {
          redirectUrl: location.href,
        }
      })
    }
  },
  component: () => <Outlet />,
})
