import { createFileRoute, Outlet, redirect } from '@tanstack/react-router'

export const Route = createFileRoute('/_auth')({
  beforeLoad: async ({ location, context }) => {
    console.log(context.auth)
    if (!context.auth.isAuthenticated) {
      throw redirect({
        to: "/login",
        search: {
          redirectUrl: location.href,
        }
      })
    }
  },
  component: () => <Outlet />,
})
