import { isAuthenticated } from '@/lib/auth'
import { createFileRoute, Outlet, redirect } from '@tanstack/react-router'

export const Route = createFileRoute('/_auth')({
  beforeLoad: async ({ location, context }) => {
    if (!isAuthenticated()) {
      return redirect({
        to: "/login",
        search: {
          redirectUrl: location.href
        }
      })
    }
  },
  component: () => <Outlet />,
})
